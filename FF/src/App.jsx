import { useCallback, useState } from 'react';
import {
  ReactFlow,
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Panel,
} from 'reactflow';

import 'reactflow/dist/style.css';
import './App.css';

const initialNodes = [
  {
    id: '1',
    type: 'input',
    data: { label: 'Start Node' },
    position: { x: 250, y: 25 },
    style: {
      background: '#6ede87',
      color: 'white',
    },
  },
  {
    id: '2',
    data: { label: 'Process Data' },
    position: { x: 100, y: 125 },
    style: {
      background: '#6366f1',
      color: 'white',
    },
  },
  {
    id: '3',
    data: { label: 'Make Decision' },
    position: { x: 400, y: 125 },
    style: {
      background: '#f59e0b',
      color: 'white',
    },
  },
  {
    id: '4',
    data: { label: 'Option A' },
    position: { x: 250, y: 250 },
    style: {
      background: '#ef4444',
      color: 'white',
    },
  },
  {
    id: '5',
    data: { label: 'Option B' },
    position: { x: 500, y: 250 },
    style: {
      background: '#8b5cf6',
      color: 'white',
    },
  },
  {
    id: '6',
    type: 'output',
    data: { label: 'End Result' },
    position: { x: 375, y: 375 },
    style: {
      background: '#06b6d4',
      color: 'white',
    },
  },
];

const initialEdges = [
  { id: 'e1-2', source: '1', target: '2', animated: true },
  { id: 'e1-3', source: '1', target: '3', animated: true },
  { id: 'e3-4', source: '3', target: '4', label: 'Yes', style: { stroke: '#ef4444' } },
  { id: 'e3-5', source: '3', target: '5', label: 'No', style: { stroke: '#8b5cf6' } },
  { id: 'e4-6', source: '4', target: '6', animated: true },
  { id: 'e5-6', source: '5', target: '6', animated: true },
  { id: 'e2-6', source: '2', target: '6', type: 'smoothstep' },
];

function App() {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [nodeCount, setNodeCount] = useState(6);

  const onConnect = useCallback(
    (params) => setEdges((eds) => addEdge(params, eds)),
    [setEdges],
  );

  const addNode = useCallback(() => {
    const newNodeId = (nodeCount + 1).toString();
    const newNode = {
      id: newNodeId,
      data: { label: `New Node ${newNodeId}` },
      position: { x: Math.random() * 400, y: Math.random() * 400 },
      style: {
        background: '#10b981',
        color: 'white',
      },
    };
    setNodes((nds) => nds.concat(newNode));
    setNodeCount(nodeCount + 1);
  }, [nodeCount, setNodes]);

  const clearNodes = useCallback(() => {
    setNodes([]);
    setEdges([]);
  }, [setNodes, setEdges]);

  const resetFlow = useCallback(() => {
    setNodes(initialNodes);
    setEdges(initialEdges);
    setNodeCount(6);
  }, [setNodes, setEdges]);

  return (
    <div style={{ width: '100vw', height: '100vh' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        fitView
      >
        <Controls />
        <MiniMap />
        <Background variant="dots" gap={12} size={1} />
        <Panel position="top-left">
          <div style={{ padding: '10px', background: 'white', borderRadius: '5px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
            <h3 style={{ margin: '0 0 10px 0' }}>React Flow Demo</h3>
            <button onClick={addNode} style={{ marginRight: '5px', padding: '5px 10px' }}>
              Add Node
            </button>
            <button onClick={clearNodes} style={{ marginRight: '5px', padding: '5px 10px' }}>
              Clear All
            </button>
            <button onClick={resetFlow} style={{ padding: '5px 10px' }}>
              Reset
            </button>
          </div>
        </Panel>
      </ReactFlow>
    </div>
  );
}

export default App;
