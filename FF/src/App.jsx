import { useCallback, useState, useEffect } from 'react';
import {
  ReactFlow,
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Panel,
  Handle,
  Position,
} from 'reactflow';

import 'reactflow/dist/style.css';
import './App.css';

// Custom Number Input Node
const NumberNode = ({ data, id }) => {
  const [value, setValue] = useState(data.value || 0);

  const handleChange = (e) => {
    const newValue = parseFloat(e.target.value) || 0;
    setValue(newValue);
    if (data.onChange) {
      data.onChange(id, newValue);
    }
  };

  // Update local state when data.value changes
  useEffect(() => {
    if (data.value !== undefined && data.value !== value) {
      setValue(data.value);
    }
  }, [data.value, value]);

  return (
    <div className="number-node">
      <Handle type="source" position={Position.Right} />
      <div>
        <label>Number:</label>
        <input
          type="number"
          value={value}
          onChange={handleChange}
          style={{ width: '60px', marginLeft: '5px' }}
        />
      </div>
      <div style={{ fontSize: '10px', color: '#666', marginTop: '2px' }}>
        Value: {value}
      </div>
    </div>
  );
};

// Custom Operation Node
const OperationNode = ({ data, id }) => {
  const [operation, setOperation] = useState(data.operation || '+');

  const handleChange = (e) => {
    const newOp = e.target.value;
    setOperation(newOp);
    if (data.onChange) {
      data.onChange(id, newOp);
    }
  };

  // Update local state when data.operation changes
  useEffect(() => {
    if (data.operation !== undefined && data.operation !== operation) {
      setOperation(data.operation);
    }
  }, [data.operation, operation]);

  return (
    <div className="operation-node">
      <Handle type="target" position={Position.Left} />
      <Handle type="target" position={Position.Top} />
      <div>
        <select value={operation} onChange={handleChange}>
          <option value="+">+</option>
          <option value="-">-</option>
          <option value="*">×</option>
          <option value="/">/</option>
        </select>
      </div>
      <div style={{ fontSize: '10px', color: '#666', marginTop: '2px' }}>
        Op: {operation}
      </div>
      <Handle type="source" position={Position.Right} />
    </div>
  );
};

// Custom Result Node
const ResultNode = ({ data, id }) => {
  const result = data.result !== undefined ? data.result : 0;
  const formattedResult = typeof result === 'number' ? result.toFixed(2) : result;

  return (
    <div className="result-node">
      <Handle type="target" position={Position.Left} />
      <Handle type="target" position={Position.Top} />
      <div>
        <strong>Result</strong>
      </div>
      <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#2e7d32' }}>
        {formattedResult}
      </div>
      <Handle type="source" position={Position.Right} />
    </div>
  );
};

const nodeTypes = {
  numberNode: NumberNode,
  operationNode: OperationNode,
  resultNode: ResultNode,
};

const initialNodes = [
  {
    id: 'num1',
    type: 'numberNode',
    data: {
      value: 10,
      onChange: () => { } // Will be set in component
    },
    position: { x: 100, y: 100 },
  },
  {
    id: 'num2',
    type: 'numberNode',
    data: {
      value: 5,
      onChange: () => { } // Will be set in component
    },
    position: { x: 100, y: 200 },
  },
  {
    id: 'op1',
    type: 'operationNode',
    data: {
      operation: '+',
      onChange: () => { } // Will be set in component
    },
    position: { x: 300, y: 150 },
  },
  {
    id: 'result1',
    type: 'resultNode',
    data: { result: 15 },
    position: { x: 500, y: 150 },
  },
  // Second calculation
  {
    id: 'num3',
    type: 'numberNode',
    data: {
      value: 20,
      onChange: () => { } // Will be set in component
    },
    position: { x: 100, y: 350 },
  },
  {
    id: 'op2',
    type: 'operationNode',
    data: {
      operation: '*',
      onChange: () => { } // Will be set in component
    },
    position: { x: 300, y: 300 },
  },
  {
    id: 'result2',
    type: 'resultNode',
    data: { result: 300 },
    position: { x: 500, y: 300 },
  },
];

const initialEdges = [
  { id: 'e-num1-op1', source: 'num1', target: 'op1', animated: true },
  { id: 'e-num2-op1', source: 'num2', target: 'op1', animated: true },
  { id: 'e-op1-result1', source: 'op1', target: 'result1', animated: true },
  { id: 'e-num3-op2', source: 'num3', target: 'op2', animated: true },
  { id: 'e-result1-op2', source: 'result1', target: 'op2', animated: true },
  { id: 'e-op2-result2', source: 'op2', target: 'result2', animated: true },
];

function App() {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [nodeCount, setNodeCount] = useState(7);
  const [nodeValues, setNodeValues] = useState({
    num1: 10,
    num2: 5,
    num3: 20,
    op1: '+',
    op2: '*'
  });

  // Calculate results based on current values and connections
  const calculateResults = useCallback(() => {
    const results = {};

    // Get all result nodes
    const resultNodes = nodes.filter(node => node.type === 'resultNode');

    resultNodes.forEach(resultNode => {
      // Find all edges that connect to this result node
      const incomingEdges = edges.filter(edge => edge.target === resultNode.id);

      if (incomingEdges.length === 0) {
        results[resultNode.id] = 0;
        return;
      }

      // If there's only one input, just pass the value through
      if (incomingEdges.length === 1) {
        const sourceNode = nodes.find(node => node.id === incomingEdges[0].source);
        if (sourceNode) {
          if (sourceNode.type === 'numberNode') {
            results[resultNode.id] = nodeValues[sourceNode.id] || 0;
          } else if (sourceNode.type === 'operationNode') {
            // Calculate operation result
            const opInputs = edges.filter(edge => edge.target === sourceNode.id);
            if (opInputs.length >= 2) {
              const values = opInputs.map(edge => {
                const inputNode = nodes.find(node => node.id === edge.source);
                if (inputNode?.type === 'numberNode') {
                  return nodeValues[inputNode.id] || 0;
                } else if (inputNode?.type === 'resultNode') {
                  return results[inputNode.id] || 0;
                }
                return 0;
              });

              const operation = nodeValues[sourceNode.id] || '+';
              let result = values[0];
              for (let i = 1; i < values.length; i++) {
                switch (operation) {
                  case '+': result += values[i]; break;
                  case '-': result -= values[i]; break;
                  case '*': result *= values[i]; break;
                  case '/': result = values[i] !== 0 ? result / values[i] : 0; break;
                }
              }
              results[resultNode.id] = result;
            } else {
              results[resultNode.id] = 0;
            }
          } else if (sourceNode.type === 'resultNode') {
            results[resultNode.id] = results[sourceNode.id] || 0;
          }
        }
      }
    });

    return results;
  }, [nodes, edges, nodeValues]);

  // Update node data when values change
  const handleNodeChange = useCallback((nodeId, value) => {
    setNodeValues(prev => ({ ...prev, [nodeId]: value }));
  }, []);

  // Update nodes with calculated results and onChange handlers
  useEffect(() => {
    const results = calculateResults();

    setNodes(nds => nds.map(node => ({
      ...node,
      data: {
        ...node.data,
        onChange: handleNodeChange,
        value: nodeValues[node.id] !== undefined ? nodeValues[node.id] : node.data.value,
        operation: nodeValues[node.id] !== undefined ? nodeValues[node.id] : node.data.operation,
        result: results[node.id] !== undefined ? results[node.id] : node.data.result
      }
    })));
  }, [nodeValues, calculateResults, handleNodeChange, setNodes]);

  const onConnect = useCallback(
    (params) => setEdges((eds) => addEdge(params, eds)),
    [setEdges],
  );

  const addNumberNode = useCallback(() => {
    const newNodeId = `num${nodeCount}`;
    const initialValue = 0;
    const newNode = {
      id: newNodeId,
      type: 'numberNode',
      data: {
        value: initialValue,
        onChange: handleNodeChange
      },
      position: { x: Math.random() * 300 + 50, y: Math.random() * 300 + 50 },
    };
    setNodes((nds) => nds.concat(newNode));
    setNodeValues(prev => ({ ...prev, [newNodeId]: initialValue }));
    setNodeCount(nodeCount + 1);
  }, [nodeCount, setNodes, handleNodeChange]);

  const addOperationNode = useCallback(() => {
    const newNodeId = `op${nodeCount}`;
    const initialOp = '+';
    const newNode = {
      id: newNodeId,
      type: 'operationNode',
      data: {
        operation: initialOp,
        onChange: handleNodeChange
      },
      position: { x: Math.random() * 300 + 200, y: Math.random() * 300 + 50 },
    };
    setNodes((nds) => nds.concat(newNode));
    setNodeValues(prev => ({ ...prev, [newNodeId]: initialOp }));
    setNodeCount(nodeCount + 1);
  }, [nodeCount, setNodes, handleNodeChange]);

  const addResultNode = useCallback(() => {
    const newNodeId = `result${nodeCount}`;
    const newNode = {
      id: newNodeId,
      type: 'resultNode',
      data: { result: 0 },
      position: { x: Math.random() * 300 + 400, y: Math.random() * 300 + 50 },
    };
    setNodes((nds) => nds.concat(newNode));
    setNodeCount(nodeCount + 1);
  }, [nodeCount, setNodes]);

  const clearNodes = useCallback(() => {
    setNodes([]);
    setEdges([]);
    setNodeValues({});
  }, [setNodes, setEdges]);

  const resetFlow = useCallback(() => {
    setNodes(initialNodes);
    setEdges(initialEdges);
    setNodeCount(7);
    setNodeValues({
      num1: 10,
      num2: 5,
      num3: 20,
      op1: '+',
      op2: '*'
    });
  }, [setNodes, setEdges]);

  return (
    <div style={{ width: '100vw', height: '100vh' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        fitView
      >
        <Controls />
        <MiniMap />
        <Background variant="dots" gap={12} size={1} />
        <Panel position="top-left">
          <div style={{ padding: '10px', background: 'white', borderRadius: '5px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
            <h3 style={{ margin: '0 0 10px 0' }}>Calculator Flow</h3>
            <div style={{ marginBottom: '10px' }}>
              <button onClick={addNumberNode} style={{ marginRight: '5px', padding: '5px 10px', fontSize: '12px' }}>
                + Number
              </button>
              <button onClick={addOperationNode} style={{ marginRight: '5px', padding: '5px 10px', fontSize: '12px' }}>
                + Operation
              </button>
              <button onClick={addResultNode} style={{ marginRight: '5px', padding: '5px 10px', fontSize: '12px' }}>
                + Result
              </button>
            </div>
            <div>
              <button onClick={clearNodes} style={{ marginRight: '5px', padding: '5px 10px', fontSize: '12px' }}>
                Clear All
              </button>
              <button onClick={resetFlow} style={{ padding: '5px 10px', fontSize: '12px' }}>
                Reset
              </button>
            </div>
          </div>
        </Panel>
      </ReactFlow>
    </div>
  );
}

export default App;
