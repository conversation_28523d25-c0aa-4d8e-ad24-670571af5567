/* Reset styles for React Flow */
#root {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
}

/* Custom styles for React Flow nodes */
.react-flow__node {
  border-radius: 8px;
  border: 2px solid #fff;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  font-weight: 600;
  font-size: 14px;
}

.react-flow__node:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

.react-flow__edge-path {
  stroke-width: 2;
}

.react-flow__edge.animated path {
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

@keyframes dashdraw {
  to {
    stroke-dashoffset: -10;
  }
}

/* Panel styling */
.react-flow__panel {
  z-index: 10;
}

/* Controls styling */
.react-flow__controls {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* MiniMap styling */
.react-flow__minimap {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

/* Custom Node Styles */
.number-node {
  background: #e0f2fe;
  border: 2px solid #0288d1;
  border-radius: 8px;
  padding: 12px;
  min-width: 130px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: 'Arial', sans-serif;
}

.number-node label {
  display: block;
  font-weight: bold;
  color: #0277bd;
  margin-bottom: 5px;
  font-size: 12px;
}

.number-node input {
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  width: 70px;
}

.operation-node {
  background: #fff3e0;
  border: 2px solid #f57c00;
  border-radius: 8px;
  padding: 12px;
  min-width: 90px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: 'Arial', sans-serif;
}

.operation-node select {
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 18px;
  font-weight: bold;
  background: white;
  text-align: center;
  width: 50px;
}

.result-node {
  background: #e8f5e8;
  border: 2px solid #4caf50;
  border-radius: 8px;
  padding: 12px;
  min-width: 130px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: 'Arial', sans-serif;
}

.result-node strong {
  color: #2e7d32;
  font-size: 12px;
  display: block;
  margin-bottom: 5px;
}

/* Handle styling */
.react-flow__handle {
  width: 8px;
  height: 8px;
  background: #555;
  border: 2px solid #fff;
}

.react-flow__handle-top {
  top: -5px;
}

.react-flow__handle-bottom {
  bottom: -5px;
}

.react-flow__handle-left {
  left: -5px;
}

.react-flow__handle-right {
  right: -5px;
}