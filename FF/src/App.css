/* Reset styles for React Flow */
#root {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
}

/* Custom styles for React Flow nodes */
.react-flow__node {
  border-radius: 8px;
  border: 2px solid #fff;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  font-weight: 600;
  font-size: 14px;
}

.react-flow__node:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

.react-flow__edge-path {
  stroke-width: 2;
}

.react-flow__edge.animated path {
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

@keyframes dashdraw {
  to {
    stroke-dashoffset: -10;
  }
}

/* Panel styling */
.react-flow__panel {
  z-index: 10;
}

/* Controls styling */
.react-flow__controls {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* MiniMap styling */
.react-flow__minimap {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}
